# Commerce App - API Integration Documentation

## Overview

The Commerce app integrates with external APIs to fetch product data and provides robust fallback mechanisms to ensure a seamless user experience even when network connectivity is limited.

## External API Integration

### Fake Store API

**Base URL**: `https://fakestoreapi.com`

The app primarily uses the Fake Store API, a free REST API for testing and prototyping e-commerce applications.

#### Endpoints Used

##### 1. Get All Products
```http
GET https://fakestoreapi.com/products
```

**Response Format:**
```json
[
  {
    "id": 1,
    "title": "Fjallraven - Foldsack No. 1 Backpack, Fits 15 Laptops",
    "price": 109.95,
    "description": "Your perfect pack for everyday use...",
    "category": "men's clothing",
    "image": "https://fakestoreapi.com/img/81fPKd-2AYL._AC_SL1500_.jpg",
    "rating": {
      "rate": 3.9,
      "count": 120
    }
  }
]
```

**Implementation Location**: `lib/providers/product_provider.dart`

```dart
Future<void> fetchProducts() async {
  _isLoading = true;
  _error = '';
  notifyListeners();

  try {
    final response = await http.get(
      Uri.parse('https://fakestoreapi.com/products'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> productData = json.decode(response.body);
      _products = productData.map((data) => Product.fromJson(data)).toList();
      _error = '';
    } else {
      _error = 'Failed to load products';
      _loadDummyProducts(); // Fallback mechanism
    }
  } catch (error) {
    _error = 'Network error: $error';
    _loadDummyProducts(); // Fallback mechanism
  }

  _isLoading = false;
  notifyListeners();
}
```

## Data Models & Serialization

### Product Model

**File**: `lib/models/product.dart`

```dart
class Product {
  final String id;
  final String title;
  final String description;
  final double price;
  final String imageUrl;
  final String category;
  final double rating;
  final int stock;
  bool isFavorite;

  // JSON Serialization
  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'].toString(),
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      imageUrl: json['image'] ?? '',
      category: json['category'] ?? '',
      rating: (json['rating']?['rate'] ?? 0).toDouble(),
      stock: json['stock'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'price': price,
      'image': imageUrl,
      'category': category,
      'rating': {'rate': rating},
      'stock': stock,
    };
  }
}
```

### API Response Mapping

| API Field | Model Field | Type | Notes |
|-----------|-------------|------|-------|
| `id` | `id` | String | Converted to string for consistency |
| `title` | `title` | String | Product name |
| `description` | `description` | String | Product description |
| `price` | `price` | double | Product price |
| `image` | `imageUrl` | String | Product image URL |
| `category` | `category` | String | Product category |
| `rating.rate` | `rating` | double | Average rating |
| `rating.count` | - | - | Not used in current implementation |

## Error Handling & Fallback Mechanisms

### Network Error Handling

The app implements a comprehensive error handling strategy:

#### 1. HTTP Status Code Handling
```dart
if (response.statusCode == 200) {
  // Success - parse data
} else {
  _error = 'Failed to load products';
  _loadDummyProducts();
}
```

#### 2. Network Exception Handling
```dart
try {
  // API call
} catch (error) {
  _error = 'Network error: $error';
  _loadDummyProducts();
}
```

#### 3. JSON Parsing Error Handling
```dart
factory Product.fromJson(Map<String, dynamic> json) {
  return Product(
    id: json['id'].toString(),
    title: json['title'] ?? '', // Default empty string
    description: json['description'] ?? '',
    price: (json['price'] ?? 0).toDouble(), // Default 0
    // ... other fields with null safety
  );
}
```

### Fallback Data System

When the API is unavailable, the app loads predefined dummy data:

```dart
void _loadDummyProducts() {
  _products = [
    Product(
      id: '1',
      title: 'Smartphone',
      description: 'Latest model smartphone with advanced features',
      price: 699.99,
      imageUrl: 'https://via.placeholder.com/300x300?text=Smartphone',
      category: 'Electronics',
      rating: 4.5,
      stock: 10,
    ),
    // ... more dummy products
  ];
}
```

## Network Layer Implementation

### HTTP Client Configuration

**Dependencies** (`pubspec.yaml`):
```yaml
dependencies:
  http: ^1.1.0
```

**Import and Usage**:
```dart
import 'package:http/http.dart' as http;
import 'dart:convert';
```

### Request Configuration

#### Headers
- Content-Type: application/json (for future POST requests)
- Accept: application/json

#### Timeout Handling
```dart
final response = await http.get(
  Uri.parse('https://fakestoreapi.com/products'),
).timeout(
  const Duration(seconds: 10),
  onTimeout: () {
    throw TimeoutException('Request timeout');
  },
);
```

### Response Processing

#### Success Response (200)
1. Parse JSON response body
2. Map to Product objects using `fromJson`
3. Update provider state
4. Notify listeners for UI updates

#### Error Response (4xx, 5xx)
1. Log error details
2. Set error message in provider
3. Load fallback dummy data
4. Notify listeners

#### Network Exception
1. Catch and log exception
2. Set user-friendly error message
3. Load fallback dummy data
4. Notify listeners

## State Management Integration

### Provider Pattern Implementation

The API integration is seamlessly integrated with the Provider state management:

```dart
class ProductProvider with ChangeNotifier {
  List<Product> _products = [];
  bool _isLoading = false;
  String _error = '';

  // Getters
  List<Product> get products => [..._products];
  bool get isLoading => _isLoading;
  String get error => _error;

  // API Integration
  Future<void> fetchProducts() async {
    // Implementation shown above
  }
}
```

### UI Integration

Screens consume the provider and react to state changes:

```dart
class HomeScreen extends StatefulWidget {
  @override
  void didChangeDependencies() {
    if (_isInit) {
      setState(() {
        _isLoading = true;
      });
      Provider.of<ProductProvider>(context).fetchProducts().then((_) {
        setState(() {
          _isLoading = false;
        });
      });
    }
    _isInit = false;
    super.didChangeDependencies();
  }
}
```

## Performance Optimizations

### Caching Strategy

1. **In-Memory Caching**: Products cached in provider during app session
2. **Image Caching**: Flutter's built-in image caching for network images
3. **Lazy Loading**: Products loaded only when needed

### Network Optimization

1. **Single API Call**: Fetch all products once, filter locally
2. **Efficient Parsing**: Direct JSON to object mapping
3. **Error Recovery**: Graceful degradation with fallback data

## Future API Enhancements

### Planned Integrations

1. **Authentication API**: User login/signup
2. **Cart Persistence API**: Server-side cart storage
3. **Order Management API**: Order placement and tracking
4. **Payment Gateway**: Stripe/PayPal integration
5. **Product Search API**: Advanced search capabilities
6. **Reviews API**: User-generated product reviews

### API Architecture Improvements

1. **Repository Pattern**: Abstract data sources
2. **Dio Integration**: Advanced HTTP client with interceptors
3. **GraphQL**: More efficient data fetching
4. **Offline Support**: Local database with sync
5. **Real-time Updates**: WebSocket integration for live data

## Testing API Integration

### Unit Tests

```dart
test('ProductProvider fetches products successfully', () async {
  final provider = ProductProvider();
  await provider.fetchProducts();
  
  expect(provider.products.isNotEmpty, true);
  expect(provider.error, '');
  expect(provider.isLoading, false);
});
```

### Mock API Testing

```dart
test('ProductProvider handles API errors gracefully', () async {
  // Mock HTTP client to return error
  final provider = ProductProvider();
  await provider.fetchProducts();
  
  expect(provider.products.isNotEmpty, true); // Fallback data
  expect(provider.error.isNotEmpty, true);
});
```
