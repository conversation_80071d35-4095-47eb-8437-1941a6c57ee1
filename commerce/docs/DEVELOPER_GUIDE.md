# Commerce App - Developer Guide

## Getting Started

### Prerequisites

Before you begin, ensure you have the following installed:

- **Flutter SDK** (3.0.0+): [Installation Guide](https://docs.flutter.dev/get-started/install)
- **Dart SDK** (2.17.0+): Included with Flutter
- **Android Studio** or **VS Code**: IDE with Flutter plugins
- **Git**: Version control system
- **Android SDK** (API 21+): For Android development
- **Xcode** (13.0+): For iOS development (macOS only)

### Environment Setup

#### 1. Verify Flutter Installation
```bash
flutter doctor
```
Ensure all checkmarks are green or address any issues shown.

#### 2. <PERSON>lone the Repository
```bash
git clone <repository-url>
cd commerce
```

#### 3. Install Dependencies
```bash
flutter pub get
```

#### 4. Verify Setup
```bash
flutter analyze
flutter test
```

## Development Environment

### IDE Configuration

#### VS Code Extensions
- Flutter
- Dart
- Flutter Widget Snippets
- Bracket Pair Colorizer
- GitLens

#### Android Studio Plugins
- Flutter
- Dart
- Rainbow Brackets
- Git Toolbox

### Project Structure Setup

```
commerce/
├── lib/                    # Main application code
│   ├── main.dart          # App entry point
│   ├── models/            # Data models
│   ├── providers/         # State management
│   ├── screens/           # UI screens
│   └── widgets/           # Reusable components
├── test/                  # Test files
├── android/               # Android-specific code
├── ios/                   # iOS-specific code
├── assets/                # Static assets
└── docs/                  # Documentation
```

## Running the Application

### Development Mode

#### Run on Android Emulator
```bash
# Start Android emulator first
flutter run
```

#### Run on iOS Simulator (macOS only)
```bash
# Start iOS simulator first
flutter run
```

#### Run on Physical Device
```bash
# Enable developer mode and USB debugging
flutter run
```

#### Hot Reload
- Press `r` in terminal for hot reload
- Press `R` for hot restart
- Use IDE shortcuts (Ctrl+S in VS Code)

### Debug Mode Features

#### Flutter Inspector
- Widget tree visualization
- Property inspection
- Layout debugging

#### Debug Console
```bash
flutter logs
```

#### Performance Overlay
```dart
// Add to MaterialApp
debugShowCheckedModeBanner: false,
showPerformanceOverlay: true, // Enable for performance monitoring
```

## Code Organization

### File Naming Conventions

- **Screens**: `*_screen.dart` (e.g., `home_screen.dart`)
- **Widgets**: `*_widget.dart` or descriptive names (e.g., `product_card.dart`)
- **Providers**: `*_provider.dart` (e.g., `cart_provider.dart`)
- **Models**: Descriptive names (e.g., `product.dart`)

### Import Organization

```dart
// 1. Dart imports
import 'dart:convert';

// 2. Flutter imports
import 'package:flutter/material.dart';

// 3. Package imports
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;

// 4. Local imports
import '../models/product.dart';
import '../providers/cart_provider.dart';
```

### Code Style Guidelines

#### Widget Structure
```dart
class ProductCard extends StatelessWidget {
  final Product product;

  const ProductCard({
    super.key,
    required this.product,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      // Widget implementation
    );
  }
}
```

#### Provider Structure
```dart
class ProductProvider with ChangeNotifier {
  // Private fields
  List<Product> _products = [];
  bool _isLoading = false;

  // Public getters
  List<Product> get products => [..._products];
  bool get isLoading => _isLoading;

  // Public methods
  Future<void> fetchProducts() async {
    // Implementation
    notifyListeners();
  }
}
```

## Testing Procedures

### Running Tests

#### All Tests
```bash
flutter test
```

#### Specific Test File
```bash
flutter test test/widget_test.dart
```

#### Test with Coverage
```bash
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

### Writing Tests

#### Unit Tests
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:commerce/providers/cart_provider.dart';

void main() {
  group('CartProvider Tests', () {
    test('should add item to cart', () {
      final cart = CartProvider();
      final product = Product(/* test data */);
      
      cart.addItem(product);
      
      expect(cart.itemCount, 1);
    });
  });
}
```

#### Widget Tests
```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:commerce/widgets/product_card.dart';

void main() {
  testWidgets('ProductCard displays product info', (WidgetTester tester) async {
    final product = Product(/* test data */);
    
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: ProductCard(product: product),
        ),
      ),
    );
    
    expect(find.text(product.title), findsOneWidget);
  });
}
```

### Test Coverage Goals

- **Unit Tests**: 80%+ coverage for providers and models
- **Widget Tests**: All custom widgets tested
- **Integration Tests**: Critical user flows covered

## Debugging Guide

### Common Issues & Solutions

#### 1. Provider Not Found Error
```
Error: Could not find the correct Provider<ProductProvider>
```
**Solution**: Ensure provider is declared in widget tree above consumer.

#### 2. Network Request Failures
```
Error: SocketException: Failed host lookup
```
**Solution**: Check internet connectivity and API endpoint.

#### 3. Image Loading Issues
```
Error: Unable to load asset
```
**Solution**: Verify image URLs and implement error handling.

### Debugging Tools

#### Flutter Inspector
```bash
flutter inspector
```

#### Debug Prints
```dart
debugPrint('Debug message: $variable');
```

#### Breakpoints
- Set breakpoints in IDE
- Use `debugger()` statement in code

#### Performance Profiling
```bash
flutter run --profile
```

## State Management Best Practices

### Provider Usage

#### Creating Providers
```dart
// In main.dart
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (ctx) => ProductProvider()),
    ChangeNotifierProvider(create: (ctx) => CartProvider()),
  ],
  child: MyApp(),
)
```

#### Consuming Providers
```dart
// For rebuilding on changes
Consumer<ProductProvider>(
  builder: (ctx, productProvider, child) {
    return Text('${productProvider.products.length} products');
  },
)

// For one-time access
final cartProvider = Provider.of<CartProvider>(context, listen: false);
cartProvider.addItem(product);
```

#### Optimizing Rebuilds
```dart
// Use Selector for specific properties
Selector<CartProvider, int>(
  selector: (ctx, cart) => cart.itemCount,
  builder: (ctx, itemCount, child) {
    return Text('Items: $itemCount');
  },
)
```

### State Management Patterns

#### Immutable State Updates
```dart
// Good: Create new list
_products = [..._products, newProduct];

// Bad: Mutate existing list
_products.add(newProduct);
```

#### Error Handling
```dart
try {
  await fetchProducts();
} catch (error) {
  _error = 'Failed to load products';
  notifyListeners();
}
```

## API Integration Guidelines

### HTTP Client Setup
```dart
import 'package:http/http.dart' as http;

class ApiService {
  static const String baseUrl = 'https://fakestoreapi.com';
  
  static Future<List<Product>> fetchProducts() async {
    final response = await http.get(Uri.parse('$baseUrl/products'));
    
    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.map((json) => Product.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load products');
    }
  }
}
```

### Error Handling Strategy
```dart
Future<void> fetchProducts() async {
  _isLoading = true;
  _error = '';
  notifyListeners();

  try {
    _products = await ApiService.fetchProducts();
  } catch (error) {
    _error = 'Network error occurred';
    _loadFallbackData(); // Provide fallback
  } finally {
    _isLoading = false;
    notifyListeners();
  }
}
```

## Performance Optimization

### Widget Optimization

#### Use const Constructors
```dart
const ProductCard({
  super.key,
  required this.product,
});
```

#### Implement shouldRebuild
```dart
class ProductCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<ProductProvider>(
      builder: (ctx, provider, child) {
        // Only rebuild when necessary
      },
    );
  }
}
```

### List Performance

#### Use Builder Constructors
```dart
ListView.builder(
  itemCount: products.length,
  itemBuilder: (ctx, index) => ProductCard(product: products[index]),
)
```

#### Implement Keys for Lists
```dart
ListView.builder(
  itemBuilder: (ctx, index) => ProductCard(
    key: ValueKey(products[index].id),
    product: products[index],
  ),
)
```

## Build & Deployment

### Debug Build
```bash
flutter run --debug
```

### Release Build
```bash
# Android
flutter build apk --release
flutter build appbundle --release

# iOS
flutter build ios --release
```

### Build Optimization
```bash
# Analyze bundle size
flutter build apk --analyze-size

# Enable obfuscation
flutter build apk --obfuscate --split-debug-info=build/debug-info
```

## Contributing Guidelines

### Code Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No debug prints in production code
- [ ] Error handling is implemented
- [ ] Performance considerations addressed

### Git Workflow

```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/new-feature
```

### Commit Message Format
```
type(scope): description

feat: add new feature
fix: resolve bug
docs: update documentation
test: add tests
refactor: improve code structure
```

## Troubleshooting

### Common Development Issues

#### Gradle Build Failures
```bash
cd android
./gradlew clean
cd ..
flutter clean
flutter pub get
```

#### iOS Build Issues
```bash
cd ios
rm -rf Pods
rm Podfile.lock
pod install
cd ..
flutter clean
flutter pub get
```

#### Hot Reload Not Working
```bash
flutter clean
flutter pub get
flutter run
```

### Performance Issues

#### Memory Leaks
- Use Flutter Inspector to identify widget rebuilds
- Implement proper disposal in StatefulWidgets
- Avoid creating objects in build methods

#### Slow Scrolling
- Use ListView.builder for large lists
- Implement image caching
- Optimize widget trees
