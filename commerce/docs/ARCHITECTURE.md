# Commerce App - Architecture Documentation

## Overview

The Commerce Flutter application follows a clean, scalable architecture based on the **Model-View-Provider (MVP)** pattern with state management handled by the Provider package. This architecture ensures separation of concerns, testability, and maintainability.

## Architecture Pattern

### Model-View-Provider (MVP) Implementation

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      View       │    │    Provider     │    │     Model       │
│   (Widgets/     │◄──►│  (Business      │◄──►│  (Data Layer)   │
│   Screens)      │    │   Logic)        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

- **Model**: Data structures and business entities (`Product`, `CartItem`)
- **View**: UI components (Screens, Widgets)
- **Provider**: State management and business logic (`ProductProvider`, `CartProvider`)

## Directory Structure

```
commerce/
├── lib/
│   ├── main.dart                 # Application entry point
│   ├── models/                   # Data models and entities
│   │   ├── product.dart          # Product data model
│   │   └── cart_item.dart        # Cart item data model
│   ├── providers/                # State management layer
│   │   ├── product_provider.dart # Product state management
│   │   └── cart_provider.dart    # Cart state management
│   ├── screens/                  # Main application screens
│   │   ├── home_screen.dart      # Home/landing screen
│   │   ├── product_list_screen.dart # Product catalog screen
│   │   ├── product_detail_screen.dart # Product details screen
│   │   └── cart_screen.dart      # Shopping cart screen
│   └── widgets/                  # Reusable UI components
│       ├── product_card.dart     # Product display component
│       ├── cart_item_widget.dart # Cart item component
│       └── category_chip.dart    # Category filter component
├── test/                         # Unit and widget tests
├── android/                      # Android-specific configuration
├── assets/                       # Static assets (images, icons)
├── docs/                         # Documentation files
└── pubspec.yaml                  # Project dependencies and metadata
```

### File Purpose Breakdown

#### Core Application Files
- **`main.dart`**: Application bootstrap, provider setup, routing configuration
- **`pubspec.yaml`**: Dependencies, assets, and project metadata

#### Models Layer (`/models`)
- **`product.dart`**: Product entity with properties, JSON serialization, and business methods
- **`cart_item.dart`**: Cart item entity with quantity management and price calculations

#### Providers Layer (`/providers`)
- **`product_provider.dart`**: 
  - Product data fetching and caching
  - Search and filtering logic
  - Favorites management
  - Category organization
- **`cart_provider.dart`**:
  - Cart state management
  - Item quantity operations
  - Price calculations
  - Persistence logic

#### Screens Layer (`/screens`)
- **`home_screen.dart`**: Main dashboard with featured products and navigation
- **`product_list_screen.dart`**: Product catalog with search, filter, and sort
- **`product_detail_screen.dart`**: Detailed product view with purchase options
- **`cart_screen.dart`**: Shopping cart management and checkout

#### Widgets Layer (`/widgets`)
- **`product_card.dart`**: Reusable product display component
- **`cart_item_widget.dart`**: Cart item with quantity controls
- **`category_chip.dart`**: Category filter chip component

## Data Flow Architecture

### State Management Flow

```
User Interaction
       ↓
   UI Widget
       ↓
Provider Method Call
       ↓
Business Logic Execution
       ↓
Model State Update
       ↓
notifyListeners()
       ↓
UI Rebuild (Consumer/Selector)
```

### Product Data Flow

```
API Request → ProductProvider → Product Models → UI Components
     ↓              ↓               ↓              ↓
Error Handling → Fallback Data → State Update → User Feedback
```

### Cart Management Flow

```
Add to Cart → CartProvider → CartItem Model → Cart UI Update
     ↓             ↓              ↓              ↓
Validation → Quantity Logic → Price Calculation → Persistence
```

## Component Relationships

### Provider Dependencies

```
MultiProvider
├── ProductProvider (Independent)
└── CartProvider (Independent)
```

### Screen Navigation Flow

```
HomeScreen
├── ProductListScreen (via category/search)
│   └── ProductDetailScreen (via product selection)
│       └── CartScreen (via add to cart)
└── CartScreen (via cart icon)
```

### Widget Hierarchy

```
MaterialApp
└── Scaffold
    ├── AppBar (with cart badge)
    ├── Body
    │   ├── ProductCard (multiple instances)
    │   ├── CategoryChip (multiple instances)
    │   └── CartItemWidget (in cart screen)
    └── BottomNavigationBar (context-specific)
```

## State Management Strategy

### Provider Pattern Implementation

1. **ChangeNotifier**: Base class for all providers
2. **Consumer**: Rebuilds UI when provider state changes
3. **Selector**: Optimized rebuilds for specific state properties
4. **Provider.of()**: Direct access to provider methods

### State Persistence

- **Cart State**: Maintained in memory during app session
- **Favorites**: Stored in provider state
- **Product Cache**: Temporary storage for API responses

## Performance Considerations

### Optimization Strategies

1. **Lazy Loading**: Products loaded on demand
2. **Image Caching**: Network images cached automatically
3. **Selective Rebuilds**: Using Selector for targeted updates
4. **List Virtualization**: GridView.builder for large product lists
5. **State Granularity**: Separate providers for different concerns

### Memory Management

- Proper disposal of providers and controllers
- Image memory optimization with error handling
- Efficient list rendering with builder patterns

## Scalability Features

### Extensibility Points

1. **New Providers**: Easy addition of new state management layers
2. **Screen Addition**: Consistent routing and navigation patterns
3. **Widget Library**: Reusable component architecture
4. **API Integration**: Abstracted network layer for easy endpoint changes

### Future Architecture Enhancements

1. **Repository Pattern**: Abstract data sources
2. **Dependency Injection**: Service locator pattern
3. **Bloc Pattern**: Alternative state management
4. **Clean Architecture**: Domain/Data/Presentation layers
