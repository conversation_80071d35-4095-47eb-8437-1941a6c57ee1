import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/product.dart';

class ProductProvider with ChangeNotifier {
  List<Product> _products = [];
  List<Product> _favoriteProducts = [];
  bool _isLoading = false;
  String _error = '';

  List<Product> get products => [..._products];
  List<Product> get favoriteProducts => [..._favoriteProducts];
  bool get isLoading => _isLoading;
  String get error => _error;

  List<Product> get featuredProducts {
    return _products.where((product) => product.rating >= 4.0).take(5).toList();
  }

  List<String> get categories {
    final Set<String> categorySet = {};
    for (var product in _products) {
      categorySet.add(product.category);
    }
    return categorySet.toList();
  }

  Product? findById(String id) {
    try {
      return _products.firstWhere((product) => product.id == id);
    } catch (error) {
      return null;
    }
  }

  List<Product> findByCategory(String category) {
    return _products.where((product) => product.category == category).toList();
  }

  List<Product> searchProducts(String query) {
    return _products
        .where((product) =>
            product.title.toLowerCase().contains(query.toLowerCase()) ||
            product.description.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  Future<void> fetchProducts() async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final response = await http.get(
        Uri.parse('https://fakestoreapi.com/products'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> productData = json.decode(response.body);
        _products = productData.map((data) => Product.fromJson(data)).toList();
        _error = '';
      } else {
        _error = 'Failed to load products';
        _loadDummyProducts(); // Fallback to dummy data
      }
    } catch (error) {
      _error = 'Network error: $error';
      _loadDummyProducts(); // Fallback to dummy data
    }

    _isLoading = false;
    notifyListeners();
  }

  void _loadDummyProducts() {
    _products = [
      Product(
        id: '1',
        title: 'Smartphone',
        description: 'Latest model smartphone with advanced features',
        price: 699.99,
        imageUrl: 'https://via.placeholder.com/300x300?text=Smartphone',
        category: 'Electronics',
        rating: 4.5,
        stock: 10,
      ),
      Product(
        id: '2',
        title: 'Laptop',
        description: 'High-performance laptop for work and gaming',
        price: 1299.99,
        imageUrl: 'https://via.placeholder.com/300x300?text=Laptop',
        category: 'Electronics',
        rating: 4.7,
        stock: 5,
      ),
      Product(
        id: '3',
        title: 'T-Shirt',
        description: 'Comfortable cotton t-shirt',
        price: 29.99,
        imageUrl: 'https://via.placeholder.com/300x300?text=T-Shirt',
        category: 'Clothing',
        rating: 4.2,
        stock: 20,
      ),
    ];
  }

  void toggleFavorite(String productId) {
    final productIndex = _products.indexWhere((prod) => prod.id == productId);
    if (productIndex >= 0) {
      _products[productIndex].isFavorite = !_products[productIndex].isFavorite;
      
      if (_products[productIndex].isFavorite) {
        _favoriteProducts.add(_products[productIndex]);
      } else {
        _favoriteProducts.removeWhere((prod) => prod.id == productId);
      }
      
      notifyListeners();
    }
  }
}
