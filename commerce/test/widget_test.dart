import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:commerce/main.dart';
import 'package:commerce/providers/cart_provider.dart';
import 'package:commerce/providers/product_provider.dart';

void main() {
  testWidgets('Commerce app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (ctx) => ProductProvider()),
          ChangeNotifierProvider(create: (ctx) => CartProvider()),
        ],
        child: const CommerceApp(),
      ),
    );

    // Verify that the app starts with the home screen
    expect(find.text('Commerce App'), findsOneWidget);
    expect(find.text('Categories'), findsOneWidget);
    expect(find.text('Featured Products'), findsOneWidget);
  });

  testWidgets('Cart provider test', (WidgetTester tester) async {
    final cartProvider = CartProvider();
    
    // Test initial state
    expect(cartProvider.itemCount, 0);
    expect(cartProvider.totalAmount, 0.0);
    expect(cartProvider.totalQuantity, 0);
  });

  testWidgets('Product provider test', (WidgetTester tester) async {
    final productProvider = ProductProvider();
    
    // Test initial state
    expect(productProvider.products, isEmpty);
    expect(productProvider.favoriteProducts, isEmpty);
    expect(productProvider.isLoading, false);
  });
}
