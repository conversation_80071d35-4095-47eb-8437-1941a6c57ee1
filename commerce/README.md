# Commerce Flutter App

A modern e-commerce mobile application built with Flutter.

## Features

- **Product Catalog**: Browse products with categories, search, and filtering
- **Product Details**: Detailed product information with images, ratings, and stock status
- **Shopping Cart**: Add/remove items, update quantities, and view cart total
- **Favorites**: Mark products as favorites for easy access
- **State Management**: Uses Provider for efficient state management
- **Responsive Design**: Optimized for different screen sizes
- **Network Integration**: Fetches products from external API with fallback data

## Architecture

The app follows a clean architecture pattern with:

- **Models**: Data structures for Product and CartItem
- **Providers**: State management using Provider pattern
- **Screens**: Main UI screens (Home, Product List, Product Detail, Cart)
- **Widgets**: Reusable UI components

## Key Components

### Models
- `Product`: Represents a product with all its properties
- `CartItem`: Represents an item in the shopping cart

### Providers
- `ProductProvider`: Manages product data, categories, and favorites
- `CartProvider`: Manages shopping cart state and operations

### Screens
- `HomeScreen`: Main landing page with featured products and categories
- `ProductListScreen`: Grid view of products with search and filtering
- `ProductDetailScreen`: Detailed view of a single product
- `CartScreen`: Shopping cart with checkout functionality

### Widgets
- `ProductCard`: Reusable product display component
- `CartItemWidget`: Individual cart item with quantity controls
- `CategoryChip`: Category filter chip component

## Getting Started

### Prerequisites
- Flutter SDK (3.0.0 or higher)
- Dart SDK
- Android Studio / VS Code
- Android device or emulator

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd commerce
   ```

2. Install dependencies:
   ```bash
   flutter pub get
   ```

3. Run the app:
   ```bash
   flutter run
   ```

## Dependencies

- `flutter`: Flutter SDK
- `provider`: State management
- `http`: HTTP requests for API calls
- `shared_preferences`: Local data persistence
- `cupertino_icons`: iOS-style icons

## API Integration

The app integrates with the [Fake Store API](https://fakestoreapi.com/) to fetch product data. If the API is unavailable, it falls back to dummy data to ensure the app remains functional.

## Features in Detail

### Product Management
- Fetch products from external API
- Search products by title and description
- Filter products by category
- Sort products by name, price, and rating
- Mark products as favorites

### Shopping Cart
- Add products to cart
- Update item quantities
- Remove items from cart
- Calculate total price
- Clear entire cart
- Persistent cart state

### User Interface
- Material Design 3 theming
- Responsive grid layouts
- Image loading with error handling
- Loading states and error handling
- Snackbar notifications
- Confirmation dialogs

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── models/                   # Data models
│   ├── product.dart
│   └── cart_item.dart
├── providers/                # State management
│   ├── product_provider.dart
│   └── cart_provider.dart
├── screens/                  # Main screens
│   ├── home_screen.dart
│   ├── product_list_screen.dart
│   ├── product_detail_screen.dart
│   └── cart_screen.dart
└── widgets/                  # Reusable widgets
    ├── product_card.dart
    ├── cart_item_widget.dart
    └── category_chip.dart
```

## Future Enhancements

- User authentication and profiles
- Order history and tracking
- Payment gateway integration
- Product reviews and ratings
- Push notifications
- Offline support
- Advanced filtering options
- Wishlist functionality
- Social sharing features

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
